{% extends "base.html" %}

{% block title %}松瓷机电AI助手 - 智能翻译{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-success text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-language me-2"></i>智能翻译
                    </h5>
                    <div class="d-flex align-items-center">
                        <button class="btn btn-outline-light btn-sm me-2" onclick="clearTranslation()">
                            <i class="fas fa-trash me-1"></i>清空
                        </button>
                        <button class="btn btn-outline-light btn-sm" onclick="swapLanguages()">
                            <i class="fas fa-exchange-alt me-1"></i>交换语言
                        </button>
                    </div>
                </div>
            </div>

            <div class="card-body">
                <!-- 语言选择 -->
                <div class="row mb-4">
                    <div class="col-md-5">
                        <label class="form-label">源语言</label>
                        <select class="form-select" id="source-lang">
                            <option value="auto">自动检测</option>
                            <option value="zh">中文</option>
                            <option value="en">英语</option>
                            <option value="ja">日语</option>
                            <option value="ko">韩语</option>
                            <option value="fr">法语</option>
                            <option value="de">德语</option>
                            <option value="es">西班牙语</option>
                            <option value="ru">俄语</option>
                        </select>
                    </div>
                    <div class="col-md-2 text-center d-flex align-items-end">
                        <button class="btn btn-outline-secondary w-100" onclick="swapLanguages()">
                            <i class="fas fa-exchange-alt"></i>
                        </button>
                    </div>
                    <div class="col-md-5">
                        <label class="form-label">目标语言</label>
                        <select class="form-select" id="target-lang">
                            <option value="zh">中文</option>
                            <option value="en" selected>英语</option>
                            <option value="ja">日语</option>
                            <option value="ko">韩语</option>
                            <option value="fr">法语</option>
                            <option value="de">德语</option>
                            <option value="es">西班牙语</option>
                            <option value="ru">俄语</option>
                        </select>
                    </div>
                </div>

                <!-- 模型选择和设置 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <label class="form-label">翻译模型</label>
                        <select class="form-select" id="translation-model-selector">
                            <option value="local_default">本地模型 (默认)</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">翻译设置</label>
                        <div class="d-flex align-items-center">
                            <div class="form-check form-switch me-3">
                                <input class="form-check-input" type="checkbox" id="use-termbase" checked>
                                <label class="form-check-label" for="use-termbase">
                                    使用术语库
                                </label>
                            </div>
                            <button class="btn btn-outline-info btn-sm" onclick="showTerminologyPreview()">
                                <i class="fas fa-book me-1"></i>术语预览
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 翻译区域 -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="translation-panel">
                            <label class="form-label">原文</label>
                            <textarea
                                class="form-control"
                                id="source-text"
                                rows="10"
                                placeholder="请输入要翻译的文本..."
                            ></textarea>
                            <div class="mt-2 d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    字符数: <span id="char-count">0</span>
                                </small>
                                <button class="btn btn-success" onclick="translateText()" id="translate-btn">
                                    <i class="fas fa-language me-1"></i>翻译
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="translation-panel">
                            <label class="form-label">译文</label>
                            <textarea
                                class="form-control"
                                id="target-text"
                                rows="10"
                                placeholder="翻译结果将显示在这里..."
                                readonly
                            ></textarea>
                            <div class="mt-2 d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <span id="translation-status"></span>
                                </small>
                                <div>
                                    <button class="btn btn-outline-secondary btn-sm me-1" onclick="copyTranslation()">
                                        <i class="fas fa-copy me-1"></i>复制
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" onclick="speakTranslation()">
                                        <i class="fas fa-volume-up me-1"></i>朗读
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 进度条 -->
                <div class="progress mt-3 d-none" id="translation-progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 100%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 翻译历史 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-history me-2"></i>翻译历史
                    </h6>
                    <button class="btn btn-outline-danger btn-sm" onclick="clearHistory()">
                        <i class="fas fa-trash me-1"></i>清空历史
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="translation-history">
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <p>暂无翻译历史</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let translationHistory = [];
let availableModels = [];
let selectedModel = 'local_default';

// 页面初始化
function initializePage() {
    console.log('初始化翻译页面');

    // 设置字符计数
    const sourceText = document.getElementById('source-text');
    sourceText.addEventListener('input', updateCharCount);

    // 加载可用模型
    loadAvailableModels();

    // 设置模型选择器事件
    const modelSelector = document.getElementById('translation-model-selector');
    modelSelector.addEventListener('change', function() {
        selectedModel = this.value;
        console.log('选择翻译模型:', selectedModel);
    });

    // 加载翻译历史
    loadTranslationHistory();

    // 监听WebSocket翻译结果
    if (socket) {
        socket.on('translation_result', function(data) {
            handleTranslationResult(data);
        });

        socket.on('translation_status', function(data) {
            updateTranslationStatus(data.message);
        });
    }
}

// 更新字符计数
function updateCharCount() {
    const sourceText = document.getElementById('source-text');
    const charCount = document.getElementById('char-count');
    charCount.textContent = sourceText.value.length;
}

// 加载可用模型
function loadAvailableModels() {
    api.get('/chat/models')
        .then(data => {
            if (data.success) {
                availableModels = data.models;
                updateTranslationModelSelector();
            }
        })
        .catch(error => {
            console.error('加载模型列表失败:', error);
        });
}

// 更新翻译模型选择器
function updateTranslationModelSelector() {
    const modelSelector = document.getElementById('translation-model-selector');
    modelSelector.innerHTML = '';

    // 添加本地默认模型
    const defaultOption = document.createElement('option');
    defaultOption.value = 'local_default';
    defaultOption.textContent = '本地模型 (默认)';
    modelSelector.appendChild(defaultOption);

    // 添加其他可用模型
    availableModels.forEach(model => {
        const option = document.createElement('option');
        option.value = model.id;
        option.textContent = `${model.name} (${getModelTypeLabel(model.type)})`;
        modelSelector.appendChild(option);
    });

    // 设置默认选择
    selectedModel = 'local_default';
    modelSelector.value = selectedModel;
}

// 获取模型类型标签
function getModelTypeLabel(type) {
    switch(type) {
        case 'local': return '本地';
        case 'ollama': return 'Ollama';
        case 'openai': return 'OpenAI';
        default: return '未知';
    }
}

// 执行翻译
function translateText() {
    const sourceText = document.getElementById('source-text').value.trim();
    const sourceLang = document.getElementById('source-lang').value;
    const targetLang = document.getElementById('target-lang').value;
    const useTermbase = document.getElementById('use-termbase').checked;

    if (!sourceText) {
        showToast('请输入要翻译的文本', 'warning');
        return;
    }

    if (sourceLang === targetLang && sourceLang !== 'auto') {
        showToast('源语言和目标语言不能相同', 'warning');
        return;
    }

    // 显示进度条
    showTranslationProgress(true);
    updateTranslationStatus('正在翻译...');

    // 禁用翻译按钮
    const translateBtn = document.getElementById('translate-btn');
    translateBtn.disabled = true;
    translateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>翻译中...';

    // 使用API进行翻译
    api.post('/translation/translate', {
        text: sourceText,
        source_lang: sourceLang,
        target_lang: targetLang,
        use_termbase: useTermbase,
        selected_model: selectedModel
    })
    .then(data => {
        if (data.success) {
            displayTranslationResult(data.translation);
            addToHistory(data.translation);
            updateTranslationStatus('翻译完成');
        } else {
            throw new Error(data.error || '翻译失败');
        }
    })
    .catch(error => {
        console.error('翻译失败:', error);
        showToast('翻译失败: ' + error.message, 'danger');
        updateTranslationStatus('翻译失败');
    })
    .finally(() => {
        // 恢复翻译按钮
        translateBtn.disabled = false;
        translateBtn.innerHTML = '<i class="fas fa-language me-1"></i>翻译';
        showTranslationProgress(false);
    });
}

// 显示翻译结果
function displayTranslationResult(translation) {
    const targetText = document.getElementById('target-text');
    targetText.value = translation.translated_text;
}

// 处理WebSocket翻译结果
function handleTranslationResult(data) {
    if (data.success) {
        displayTranslationResult(data);
        addToHistory(data);
        updateTranslationStatus('翻译完成');
    } else {
        showToast('翻译失败: ' + data.error, 'danger');
        updateTranslationStatus('翻译失败');
    }

    // 恢复翻译按钮
    const translateBtn = document.getElementById('translate-btn');
    translateBtn.disabled = false;
    translateBtn.innerHTML = '<i class="fas fa-language me-1"></i>翻译';
    showTranslationProgress(false);
}

// 显示/隐藏翻译进度条
function showTranslationProgress(show) {
    const progress = document.getElementById('translation-progress');
    if (show) {
        progress.classList.remove('d-none');
    } else {
        progress.classList.add('d-none');
    }
}

// 更新翻译状态
function updateTranslationStatus(message) {
    const status = document.getElementById('translation-status');
    status.textContent = message;
}

// 交换语言
function swapLanguages() {
    const sourceLang = document.getElementById('source-lang');
    const targetLang = document.getElementById('target-lang');
    const sourceText = document.getElementById('source-text');
    const targetText = document.getElementById('target-text');

    // 交换语言选择
    if (sourceLang.value !== 'auto') {
        const temp = sourceLang.value;
        sourceLang.value = targetLang.value;
        targetLang.value = temp;
    }

    // 交换文本内容
    const tempText = sourceText.value;
    sourceText.value = targetText.value;
    targetText.value = tempText;

    updateCharCount();
}

// 清空翻译
function clearTranslation() {
    document.getElementById('source-text').value = '';
    document.getElementById('target-text').value = '';
    updateCharCount();
    updateTranslationStatus('');
}

// 复制翻译结果
function copyTranslation() {
    const targetText = document.getElementById('target-text').value;
    if (!targetText) {
        showToast('没有翻译结果可以复制', 'warning');
        return;
    }

    utils.copyToClipboard(targetText);
}

// 朗读翻译结果
function speakTranslation() {
    const targetText = document.getElementById('target-text').value;
    if (!targetText) {
        showToast('没有翻译结果可以朗读', 'warning');
        return;
    }

    // 使用浏览器的语音合成API
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(targetText);
        const targetLang = document.getElementById('target-lang').value;

        // 设置语言
        switch (targetLang) {
            case 'zh': utterance.lang = 'zh-CN'; break;
            case 'en': utterance.lang = 'en-US'; break;
            case 'ja': utterance.lang = 'ja-JP'; break;
            case 'ko': utterance.lang = 'ko-KR'; break;
            case 'fr': utterance.lang = 'fr-FR'; break;
            case 'de': utterance.lang = 'de-DE'; break;
            case 'es': utterance.lang = 'es-ES'; break;
            case 'ru': utterance.lang = 'ru-RU'; break;
        }

        speechSynthesis.speak(utterance);
        showToast('开始朗读', 'info');
    } else {
        showToast('您的浏览器不支持语音合成', 'warning');
    }
}

// 添加到历史记录
function addToHistory(translation) {
    translationHistory.unshift(translation);

    // 限制历史记录数量
    if (translationHistory.length > 20) {
        translationHistory = translationHistory.slice(0, 20);
    }

    updateHistoryDisplay();
}

// 更新历史记录显示
function updateHistoryDisplay() {
    const historyContainer = document.getElementById('translation-history');

    if (translationHistory.length === 0) {
        historyContainer.innerHTML = `
            <div class="text-center text-muted py-3">
                <i class="fas fa-clock fa-2x mb-2"></i>
                <p>暂无翻译历史</p>
            </div>
        `;
        return;
    }

    let html = '';
    translationHistory.forEach((item, index) => {
        html += `
            <div class="border-bottom pb-2 mb-2">
                <div class="row">
                    <div class="col-md-5">
                        <small class="text-muted">原文 (${getLanguageName(item.source_lang)})</small>
                        <p class="mb-1">${escapeHtml(item.source_text)}</p>
                    </div>
                    <div class="col-md-1 text-center">
                        <i class="fas fa-arrow-right text-muted"></i>
                    </div>
                    <div class="col-md-5">
                        <small class="text-muted">译文 (${getLanguageName(item.target_lang)})</small>
                        <p class="mb-1">${escapeHtml(item.translated_text)}</p>
                    </div>
                    <div class="col-md-1">
                        <button class="btn btn-outline-secondary btn-sm" onclick="useHistoryItem(${index})">
                            <i class="fas fa-redo"></i>
                        </button>
                    </div>
                </div>
                <small class="text-muted">${utils.formatTime(item.timestamp)}</small>
            </div>
        `;
    });

    historyContainer.innerHTML = html;
}

// 使用历史记录项
function useHistoryItem(index) {
    const item = translationHistory[index];
    document.getElementById('source-text').value = item.source_text;
    document.getElementById('target-text').value = item.translated_text;
    document.getElementById('source-lang').value = item.source_lang;
    document.getElementById('target-lang').value = item.target_lang;
    updateCharCount();
}

// 获取语言名称
function getLanguageName(code) {
    const languages = {
        'auto': '自动检测',
        'zh': '中文',
        'en': '英语',
        'ja': '日语',
        'ko': '韩语',
        'fr': '法语',
        'de': '德语',
        'es': '西班牙语',
        'ru': '俄语'
    };
    return languages[code] || code;
}

// 清空历史记录
function clearHistory() {
    if (!confirm('确定要清空所有翻译历史吗？')) {
        return;
    }

    translationHistory = [];
    updateHistoryDisplay();

    // 调用API清空服务器端历史
    api.delete('/translation/clear_history')
        .then(data => {
            if (data.success) {
                showToast('翻译历史已清空', 'success');
            }
        })
        .catch(error => {
            console.error('清空历史失败:', error);
        });
}

// 加载翻译历史
function loadTranslationHistory() {
    api.get('/translation/history?per_page=20')
        .then(data => {
            if (data.success && data.history.length > 0) {
                translationHistory = data.history;
                updateHistoryDisplay();
            }
        })
        .catch(error => {
            console.error('加载翻译历史失败:', error);
        });
}

// 显示术语预览
function showTerminologyPreview() {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-book me-2"></i>术语库预览
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="terminology-preview-content">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                            <p>正在加载术语库...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 显示模态框
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // 加载术语数据
    api.get('/terminology/terms?per_page=50')
        .then(data => {
            if (data.success && data.terms.length > 0) {
                let html = '<div class="table-responsive"><table class="table table-striped">';
                html += '<thead><tr><th>源术语</th><th>目标术语</th><th>说明</th></tr></thead><tbody>';

                data.terms.forEach(term => {
                    html += `
                        <tr>
                            <td><strong>${escapeHtml(term.source_term)}</strong></td>
                            <td>${escapeHtml(term.target_term)}</td>
                            <td class="text-muted">${escapeHtml(term.definition || '无说明')}</td>
                        </tr>
                    `;
                });

                html += '</tbody></table></div>';
                html += `<div class="text-muted mt-2">共 ${data.total} 个术语，显示前 ${data.terms.length} 个</div>`;

                document.getElementById('terminology-preview-content').innerHTML = html;
            } else {
                document.getElementById('terminology-preview-content').innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-book-open fa-3x mb-3"></i>
                        <p>暂无术语数据</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('加载术语失败:', error);
            document.getElementById('terminology-preview-content').innerHTML = `
                <div class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <p>加载术语失败: ${error.message}</p>
                </div>
            `;
        });

    // 模态框关闭时移除DOM元素
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

// HTML转义
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
</script>
{% endblock %}
