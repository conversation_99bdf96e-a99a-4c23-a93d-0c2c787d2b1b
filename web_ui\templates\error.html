{% extends "base.html" %}

{% block title %}错误 {{ error_code }} - 松瓷机电AI助手{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="text-center">
            <div class="error-icon mb-4">
                {% if error_code == 404 %}
                    <i class="fas fa-search fa-5x text-warning"></i>
                {% elif error_code == 500 %}
                    <i class="fas fa-exclamation-triangle fa-5x text-danger"></i>
                {% elif error_code == 403 %}
                    <i class="fas fa-lock fa-5x text-warning"></i>
                {% else %}
                    <i class="fas fa-question-circle fa-5x text-secondary"></i>
                {% endif %}
            </div>
            
            <h1 class="display-1 fw-bold text-muted">{{ error_code }}</h1>
            
            <h2 class="mb-3">
                {% if error_code == 404 %}
                    页面不存在
                {% elif error_code == 500 %}
                    服务器内部错误
                {% elif error_code == 403 %}
                    访问被拒绝
                {% else %}
                    发生错误
                {% endif %}
            </h2>
            
            <p class="lead text-muted mb-4">
                {{ error_message }}
            </p>
            
            <div class="d-flex justify-content-center gap-3">
                <a href="{{ url_for('index') }}" class="btn btn-primary">
                    <i class="fas fa-home me-1"></i>返回首页
                </a>
                <button class="btn btn-outline-secondary" onclick="history.back()">
                    <i class="fas fa-arrow-left me-1"></i>返回上页
                </button>
                <button class="btn btn-outline-info" onclick="location.reload()">
                    <i class="fas fa-sync-alt me-1"></i>刷新页面
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
