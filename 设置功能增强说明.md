# 常规设置功能增强说明

## 概述

本次更新为WEB UI的常规设置页面添加了知识库问答配置和更多模型超参数设置，提供了更精细的控制选项。

## 新增功能

### 1. 知识库问答设置

#### 参考文件上下文数量 (kb_top_k)
- **功能**: 控制知识库问答时参考的文档片段数量
- **默认值**: 15
- **范围**: 1-50
- **说明**: 数值越大，参考的知识内容越多，但可能影响响应速度

#### 搜索相似度阈值 (kb_threshold)
- **功能**: 过滤低相似度的搜索结果
- **默认值**: 0.7
- **范围**: 0-1 (滑块控制)
- **说明**: 
  - 0: 宽松模式，接受所有搜索结果
  - 1: 严格模式，只接受高度相似的结果
  - 低于阈值的搜索结果将被过滤

#### 知识库问答温度 (kb_temperature)
- **功能**: 知识库问答专用的温度参数
- **默认值**: 0.6
- **范围**: 0-1 (滑块控制)
- **说明**: 
  - 通常比普通聊天温度更低，确保回答更严谨
  - 0: 最严谨，回答更确定性
  - 1: 更有创造性，但可能偏离知识库内容

#### 启用知识库问答 (enable_knowledge)
- **功能**: 全局开关，控制是否启用知识库问答功能
- **默认值**: 启用
- **说明**: 关闭后，知识库问答接口将返回错误

### 2. 扩展的模型超参数

#### Top-k
- **功能**: 限制候选词数量
- **默认值**: 15
- **范围**: 1-100
- **说明**: 值越小越保守，只考虑最可能的词汇

#### 重复惩罚 (Repetition Penalty)
- **功能**: 控制文本重复程度
- **默认值**: 1.1
- **范围**: 1.0-2.0 (滑块控制)
- **说明**: 
  - 1.0: 无惩罚
  - 2.0: 强惩罚，避免重复内容

#### 启用采样 (Do Sample)
- **功能**: 控制生成策略
- **默认值**: 启用
- **说明**: 
  - 启用: 使用随机采样，回答更多样化
  - 关闭: 使用贪婪解码，回答更确定性

## 技术实现

### 1. 前端界面更新

#### HTML结构
- 在 `web_ui/templates/settings.html` 中添加了新的设置区域
- 使用Bootstrap组件提供美观的界面
- 滑块控件实时显示当前值

#### JavaScript功能
- 扩展了 `setupSliders()` 函数支持新的滑块
- 更新了 `populateSettings()` 函数加载新配置
- 修改了 `saveSettings()` 函数保存新参数
- 增强了 `resetSettings()` 函数重置所有新设置

### 2. 后端API更新

#### 设置API (`web_ui/api/settings_api.py`)
- 扩展了AI相关配置键列表，包含所有新参数
- 增强了配置更新逻辑，支持知识库专用参数
- 添加了详细的日志记录

#### 知识库问答API (`web_ui/api/chat_api.py`)
- 从配置中读取知识库专用参数
- 实现了相似度阈值过滤
- 支持知识库问答开关检查

### 3. 核心引擎更新

#### 知识库引擎 (`core/knowledge_engine.py`)
- 添加了知识库专用配置属性
- 实现了 `update_settings()` 方法
- 优先使用知识库专用温度参数

#### 配置系统 (`config/settings.py`)
- 添加了所有新配置项的默认值
- 提供了详细的配置说明注释

### 4. 配置文件更新

#### config.json
- 添加了所有新的配置项
- 移除了重复的配置项
- 保持了向后兼容性

## 使用说明

### 1. 访问设置页面
- 启动WEB UI服务器
- 访问 `http://localhost:5000/settings`
- 点击"常规设置"标签页

### 2. 调整知识库问答设置
1. **参考文件上下文数量**: 根据知识库大小和响应速度需求调整
2. **搜索相似度阈值**: 根据知识库质量和准确性要求调整
3. **知识库问答温度**: 根据回答严谨性需求调整
4. **启用知识库问答**: 可临时关闭知识库功能进行测试

### 3. 调整模型超参数
1. **温度**: 控制回答的创造性
2. **Top-p**: 控制词汇选择的多样性
3. **Top-k**: 限制候选词数量
4. **重复惩罚**: 避免重复内容
5. **最大生成长度**: 控制回答长度
6. **启用采样**: 选择生成策略

### 4. 保存和重置
- 点击"保存设置"按钮应用更改
- 点击"重置"按钮恢复默认值
- 设置会立即生效，无需重启服务器

## 配置优化建议

### 知识库问答场景
- **严谨回答**: kb_temperature=0.3, kb_threshold=0.8, kb_top_k=10
- **平衡模式**: kb_temperature=0.6, kb_threshold=0.7, kb_top_k=15
- **宽松模式**: kb_temperature=0.8, kb_threshold=0.5, kb_top_k=20

### 普通聊天场景
- **保守模式**: temperature=0.3, top_p=0.6, top_k=10, repetition_penalty=1.2
- **平衡模式**: temperature=0.7, top_p=0.9, top_k=15, repetition_penalty=1.1
- **创造模式**: temperature=1.0, top_p=0.95, top_k=40, repetition_penalty=1.0

## 注意事项

1. **性能影响**: 增加kb_top_k会提高知识库搜索的计算量
2. **内存使用**: 更多的上下文会占用更多内存
3. **响应时间**: 严格的阈值可能导致某些查询无结果
4. **配置兼容**: 新配置项向后兼容，旧版本配置文件会自动补充默认值

## 故障排除

### 常见问题
1. **设置不生效**: 检查是否点击了保存按钮
2. **知识库问答失败**: 检查enable_knowledge是否启用
3. **搜索结果为空**: 降低kb_threshold阈值
4. **回答过于保守**: 提高kb_temperature值

### 日志查看
- 查看控制台输出了解配置更新状态
- 检查 `logs/web_ui.log` 文件获取详细日志
