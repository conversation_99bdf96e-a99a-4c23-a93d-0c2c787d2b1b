# 松瓷机电AI助手项目依赖要求
# 基于conda虚拟环境vllm_env (Python 3.9.22)

# UI界面
PySide6==6.9.0

# 大模型支持
transformers==4.51.3
accelerate==1.6.0
optimum==1.24.0
peft==0.5.0
safetensors==0.5.3
qwen==0.1.1
auto-gptq==0.7.1
bitsandbytes==0.45.5
bitsandbytes-windows==0.37.5
ctransformers==0.2.27
rwkv==0.4.2

# 向量模型
sentence-transformers==4.1.0
FlagEmbedding==1.3.4
einops==0.7.0

# 数据处理
numpy==1.24.3
pandas==2.2.3
jieba==0.42.1
pyperclip==1.9.0
scikit-learn==1.6.1
scipy==1.9.3
tokenizers==0.21.1
sentencepiece==0.2.0
nltk==3.9.1
regex==2024.11.6

# 文档处理
python-docx==1.1.2
pypdf2==3.0.1
lxml==5.3.2
beautifulsoup4==4.13.3

# 音频处理
torchaudio==2.5.1

# 模型加载
modelscope==1.25.0
torch==2.5.1
torchvision==0.20.1
huggingface-hub==0.30.2
pytorch-lightning==2.5.1.post0
torchmetrics==1.7.1

# 网络和工具
requests==2.32.3
tqdm==4.67.1
fsspec==2023.6.0
psutil==7.0.0
loguru==0.7.3

# 可选依赖
addict==2.4.0
pyyaml==6.0.2
rich==13.7.1

# 其他依赖
typing_extensions==4.13.2
filelock==3.18.0
packaging==24.2
pillow==11.2.1
platformdirs==4.3.7

# LLM推理引擎
llama-cpp-python==0.2.56+cpuavx2
