# 简化占位符系统实施方案

## 问题背景

根据用户反馈的实际翻译结果，现有的复杂占位符系统存在严重问题：

### 实际问题案例

1. **中译英问题**：
   - 原文：`引晶、放肩、等径都是晶体生长的过程工艺步骤`
   - 译文：`Crown, Crown , and Body are steps in the process of crystal growth.`
   - 问题：术语映射错误（引晶→Crown而非Neck）、格式异常（Crown后有空格）

2. **英译中问题**：
   - 原文：`Neck, Crown, and Body are steps in the process of crystal growth.`
   - 译文：`在晶体生长过程中，有三个步骤分别是：引晶、放肩 和 等径`
   - 问题：过度润色（添加"有三个步骤分别是"）、占位符残留（单独的空格）

3. **复杂残留问题**：
   - 原文：`Neck, Crown, Shoulder and Body are steps in the process of crystal growth.`
   - 译文：`在晶体生长过程中，步骤包括：引晶、放肩、 TER M 001 和 TERMINOLOGY 003。这里的术语似乎是特定上下文中的缩写或代码...`
   - 问题：占位符变形为`TER M 001`、`TERMINOLOGY 003`，添加大量解释文本

## 解决方案：简化且固定的占位符系统

### 核心设计原则

1. **极简格式**：使用`[T1]`, `[T2]`, `[T3]`等简单格式，避免复杂的下划线组合
2. **固定恢复**：只处理有限的几种变形，不支持复杂的正则匹配
3. **简化提示**：使用最简洁的翻译提示，避免过度润色
4. **精确映射**：确保术语顺序和对应关系正确

### 技术实现

#### 1. 占位符格式设计

```python
# 新格式：简单且不易变形
[T1], [T2], [T3], [T4]...

# 废弃格式：复杂且易变形
__TERM_001__, __TERM_002__, __TERM_003__...
```

**优势**：
- 格式简单，不容易被模型破坏
- 方括号比下划线更稳定
- 数字简化，避免三位数格式

#### 2. 术语替换逻辑

```python
def _replace_terms_with_placeholders(text, matched_terms):
    """使用固定格式占位符替换术语"""
    placeholder_map = {}
    processed_text = text
    
    # 使用固定的简单格式：[T1], [T2], [T3]...
    for i, term in enumerate(matched_terms, 1):
        placeholder = f"[T{i}]"  # 极简格式
        source_term = term['source']
        target_term = term['target']
        
        if source_term in processed_text:
            processed_text = processed_text.replace(source_term, placeholder)
            placeholder_map[placeholder] = target_term
    
    return processed_text, placeholder_map
```

#### 3. 占位符恢复逻辑

```python
def _restore_placeholders(translated_text, placeholder_map):
    """恢复占位符为目标术语 - 简化版"""
    result_text = translated_text
    
    # 第一阶段：精确匹配
    for placeholder, target_term in placeholder_map.items():
        if placeholder in result_text:
            result_text = result_text.replace(placeholder, target_term)
    
    # 第二阶段：处理简单变形（仅限5种）
    for placeholder, target_term in placeholder_map.items():
        num = re.search(r'T(\d+)', placeholder).group(1)
        simple_variants = [
            f"[T {num}]",      # 空格变形
            f"[ T{num} ]",     # 前后空格
            f"[ T {num} ]",    # 完全空格
            f"T{num}",         # 无括号
            f"T {num}",        # 无括号+空格
        ]
        
        for variant in simple_variants:
            if variant in result_text:
                result_text = result_text.replace(variant, target_term)
    
    # 第三阶段：清理残留
    cleanup_patterns = [r'\[T\s*\d+\s*\]', r'T\s*\d+']
    for pattern in cleanup_patterns:
        matches = re.findall(pattern, result_text)
        for match in matches:
            result_text = result_text.replace(match, "")
    
    return result_text
```

#### 4. 简化翻译提示

```python
# 改进前：复杂提示（导致过度润色）
prompt = """请将以下中文文本准确翻译为英文：

引晶、放肩、等径都是晶体生长的过程工艺步骤

翻译要求：
1. 保持翻译的准确性和流畅性
2. 保留原文格式和所有占位符
3. 占位符代表专业术语，必须原样保留
4. 占位符在翻译后的文本中必须保持完整的格式

请直接输出翻译结果，不要添加任何解释或标记："""

# 改进后：极简提示（避免过度润色）
prompt = """Translate to English. Keep [T1], [T2], etc. unchanged:

[T1]、[T2]、[T3]都是晶体生长的过程工艺步骤

English:"""
```

#### 5. 简化质量验证

```python
def _validate_translation_quality(translated_text, placeholder_map):
    """验证翻译质量 - 极简版"""
    issues = []
    
    # 1. 检查占位符残留
    if placeholder_map:
        for placeholder in placeholder_map.keys():
            if placeholder in translated_text:
                issues.append(f"占位符未恢复: {placeholder}")
        
        # 检查简单残留格式
        patterns = [r'\[T\s*\d+\s*\]', r'T\s*\d+', r'TER\s*M\s*\d+', r'TERMINOLOGY\s*\d+']
        for pattern in patterns:
            matches = re.findall(pattern, translated_text, re.IGNORECASE)
            if matches:
                issues.append(f"发现占位符残留: {matches}")
    
    # 2. 检查解释文本
    explanation_indicators = [
        '不过这里的术语可能需要', '如果你能提供更多的背景信息',
        '这里的术语似乎是', '请根据具体情境理解'
    ]
    for indicator in explanation_indicators:
        if indicator in translated_text:
            issues.append(f"翻译结果包含解释文本")
            break
    
    return issues
```

### 预期效果

#### 1. 解决术语映射错误

**改进前**：
- 中译英：`引晶` → `Crown`（错误）
- 英译中：`Neck` → `放肩`（错误）

**改进后**：
- 中译英：`引晶` → `Neck`（正确）
- 英译中：`Neck` → `引晶`（正确）

#### 2. 消除占位符残留

**改进前**：
- 复杂残留：`TER M 001`, `TERMINOLOGY 003`
- 格式异常：`Crown _`, `__ `

**改进后**：
- 简单格式：`[T1]`, `[T2]`, `[T3]`
- 有限变形：`[T 1]`, `[ T1 ]`, `T1`

#### 3. 避免过度润色

**改进前**：
- 添加解释：`在晶体生长过程中，有三个步骤分别是：`
- 添加说明：`这里的术语似乎是特定上下文中的缩写或代码...`

**改进后**：
- 直接翻译：`引晶、放肩和等径是晶体生长步骤`
- 无多余内容：简洁准确的翻译结果

### 测试验证结果

```
测试用例 1: 中译英测试
原文: 引晶、放肩、等径都是晶体生长的过程工艺步骤
最终结果: Neck, Crown, and Body are crystal growth process steps
✓ 术语恢复成功

测试用例 2: 英译中测试  
原文: Neck, Crown, and Body are steps in the process of crystal growth.
最终结果: 引晶、放肩和等径是晶体生长过程中的步骤。
✓ 术语恢复成功

测试用例 3: 包含四个术语的测试
原文: Neck, Crown, Shoulder and Body are steps in the process of crystal growth.
最终结果: 引晶, 放肩, 转肩 and 等径 are steps in the process of crystal growth.
✓ 术语恢复成功
```

### 部署计划

#### 阶段1：核心功能替换
- 替换占位符格式：`__TERM_001__` → `[T1]`
- 简化恢复逻辑：移除复杂的正则匹配
- 更新提示词：使用极简格式

#### 阶段2：质量验证优化
- 简化验证逻辑：只检查关键问题
- 移除复杂检查：减少误报
- 优化错误处理：提高稳定性

#### 阶段3：全面测试
- 单元测试：验证各个组件
- 集成测试：验证完整流程
- 用户测试：验证实际效果

### 风险控制

1. **向后兼容**：保留原有逻辑作为备份
2. **渐进部署**：先在测试环境验证
3. **快速回滚**：支持一键回滚到原版本
4. **监控告警**：实时监控翻译质量

### 总结

简化占位符系统通过以下方式解决了用户反馈的核心问题：

1. **固定格式**：`[T1]`, `[T2]`等简单格式，避免复杂变形
2. **精确恢复**：只处理有限变形，提高成功率
3. **简化提示**：避免过度润色和解释文本
4. **质量保证**：简化验证逻辑，减少误报

这套方案确保了翻译系统的稳定性和可靠性，为用户提供准确、简洁的翻译结果。
