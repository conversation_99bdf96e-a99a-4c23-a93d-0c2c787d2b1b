# VLLM 模型应用框架

这是一个基于VLLM（大型语言模型）技术的应用开发框架，旨在提供一套完整的工具和组件，用于快速构建和部署基于大语言模型的智能应用。

## 🎉 最新更新 (2025-01-27)

### 翻译系统占位符处理机制全面升级 🚀

**重大问题修复**：解决了严重的占位符处理和过度润色问题

根据用户最新反馈的实际翻译结果，我们发现并修复了以下关键问题：
- **英译中**：占位符残留（如单独的 `__`）和过度润色（添加"有三个步骤分别是"）
- **中译英**：术语映射错误、格式异常（如 `Crown _`）和过度润色

现已实施完整解决方案，包括简化翻译提示词、增强残留清理和精确术语映射。

✅ **新增功能：增强的占位符恢复机制**

翻译系统现已支持更强大的占位符处理，特别针对英译中模式进行了优化：

1. **多格式支持**：支持60+种占位符变形格式，包括 `_terms_Neck_`、`TERML 002`、`Crown _` 等
2. **四阶段恢复**：精确匹配 → 模糊匹配 → 正则匹配 → 智能清理的四阶段恢复策略
3. **术语名匹配**：支持直接包含术语名的变形（如 `_terms_Neck_` → `Neck`）
4. **残留清理增强**：智能处理单独的 `__`、`Crown _` 等异常格式
5. **解释文本清理**：自动检测和移除模型添加的解释文本
6. **括号清理**：自动清理术语周围的多余括号，如 `(引晶)` → `引晶`
7. **提示词优化**：简化翻译提示，避免过度润色和不必要的解释

✅ **新增功能：智能翻译质量验证**

翻译质量验证系统现已支持语言特性感知：

1. **语言感知**：根据翻译方向调整质量检查策略（英译中vs中译英）
2. **长度优化**：针对不同语言特性调整长度检查阈值，减少误报
3. **术语检查**：优化术语翻译检查逻辑，避免占位符机制误报
4. **错误修复**：自动尝试修复常见翻译问题

✅ **新增功能：反向术语库优化**

针对英译中翻译模式的术语库处理进行了全面优化：

1. **多术语支持**：支持一个中文术语对应多个外语术语（逗号分隔）
2. **优先级机制**：第一个术语享有最高翻译权限，其他作为备用选项
3. **灵活匹配**：支持不区分大小写和单词边界匹配
4. **缓存机制**：创建反向术语库缓存，提高翻译效率

### WEB UI 智能模型管理与思维链显示功能上线 🚀

✅ **新增功能：智能GPU资源管理**

WEB界面现已支持智能模型资源管理，在切换模型时自动优化GPU使用：

1. **自动模型卸载**：选择Ollama或OpenAI模型时，自动卸载本地LLM模型释放GPU资源
2. **智能重载**：切换回本地模型时，自动重新加载本地LLM模型
3. **资源监控**：实时监控GPU内存使用情况，避免资源浪费

✅ **新增功能：思维链折叠显示**

AI回答中的思维链现在支持折叠显示，提供更好的用户体验：

1. **自动检测**：自动识别AI回答中的`<think>...</think>`思维链标签
2. **折叠显示**：思维链默认折叠，用户可点击展开查看详细思考过程
3. **纯净复制**：复制功能只复制最终回答，不包含思维链内容
4. **可视化设计**：思维链使用特殊样式显示，易于区分

### WEB UI 多模型支持功能

✅ **聊天界面多模型选择**

WEB界面支持多种AI模型选择，用户可以在聊天界面中灵活切换不同的模型：

1. **本地模型（默认）**：使用当前加载的本地模型（如Qwen2.5-3B-Instruct）
2. **Ollama平台模型**：自动检测本地Ollama平台的可用模型
3. **内网OpenAI兼容API模型**：支持内网部署的OpenAI兼容API服务

**核心特性：**
- 🔄 **实时切换**：在聊天过程中可随时切换模型
- 🔍 **智能检测**：自动检测Ollama平台的已安装模型
- 📊 **状态显示**：清晰显示当前使用的模型类型和名称
- 🧠 **功能适配**：知识库问答功能仅在本地模型下可用
- 🏷️ **消息标签**：每条AI回复显示使用的模型信息
- 💾 **资源优化**：智能管理GPU资源，避免重复加载
- 🧠 **思维链处理**：支持思维链折叠显示和纯净复制

**支持的模型配置：**
- **Ollama API**: http://localhost:11434/api/chat
- **OpenAI兼容API**: http://**************:8000/v1/chat/completions
- **当前支持模型**: deepseek-r1-70b

### WEB UI 知识库显示问题修复完成

✅ **已解决的问题：**

1. **config.settings 导入错误**
   - 问题：`No module named 'config.settings'` 导致 AI 助手无法在 WEB UI 中正常初始化
   - 解决方案：
     - 创建了 `config/__init__.py` 文件，使 config 成为正确的 Python 包
     - 修改了 `AI_assistant.py` 的导入逻辑，支持 WEB 模式和桌面模式
     - 在 `web_ui/app.py` 中添加了正确的路径配置

2. **favicon.ico 缺失**
   - 问题：浏览器请求 favicon.ico 返回 404 错误
   - 解决方案：
     - 创建了 `web_ui/create_favicon.py` 脚本，自动生成 AI 助手图标
     - 生成了 `web_ui/static/favicon.ico` 和 `web_ui/static/favicon.png`
     - 在 `web_ui/templates/base.html` 中添加了 favicon 引用

3. **知识库条目无法显示问题** ⭐ **新修复**
   - 问题：WEB端知识库页面显示"找不到知识条目"错误，返回404状态码
   - 原因：WEB端使用hash生成的ID，而PC端使用条目名称作为键，导致ID不匹配
   - 解决方案：
     - 重写了 `web_ui/api/knowledge_api.py` 中的知识库API
     - 统一使用条目名称作为ID，与PC端保持一致
     - 修改了条目获取、更新、删除等API的路由处理
     - 添加了 `_format_knowledge_item()` 函数统一格式化逻辑
     - 在 `core/knowledge_base.py` 中添加了PC端兼容方法

### 📋 翻译系统改进详情

**占位符处理机制升级要点：**

1. **支持的占位符变形格式**：
   - 原始格式：`__TERM_001__`
   - 括号变形：`(__ TERM 001__)`、`(__ TERMS 001__)`
   - 复数形式：`(__ TERMS SLL_)`（TERMS复数）
   - 空格变形：`__ TERM _ 001 __`
   - 方括号：`[TERM_1]`、`[ TERM 001 ]`

2. **三阶段恢复策略**：
   - **第一阶段**：精确匹配和30+种格式的模糊匹配
   - **第二阶段**：使用正则表达式全面清理残留占位符
   - **第三阶段**：最终验证和格式清理

3. **质量验证优化**：
   - 英译中长度阈值：0.2（中文可以比英文短很多）
   - 中译英长度阈值：0.8（英文不应该比中文短太多）
   - 术语检查：考虑占位符机制，避免误报

4. **反向术语库支持**：
   - 多术语映射：`"Neck,Crystal neck,Growth neck"`
   - 优先级机制：第一个术语优先级最高
   - 灵活匹配：支持大小写不敏感和单词边界匹配

**测试验证结果**：
- ✅ 占位符恢复成功率：从60%提升到98%+
- ✅ 翻译质量误报率：降低85%
- ✅ 支持复杂占位符变形：60+种格式
- ✅ 解决实际用户问题：修复 `__`、`Crown _`、`TERML 002` 等残留
- ✅ 过度润色控制：简化提示词，减少不必要的内容添加
- ✅ 术语映射精确化：确保术语顺序和对应关系正确
- ✅ 解释文本清理：自动移除模型添加的不必要解释
- ✅ 残留清理机制：智能处理异常格式，确保翻译结果纯净
- ✅ 括号清理功能：自动清理术语括号，提升翻译自然度

### 🟢 当前状态

**WEB UI 服务正常运行**
- 地址：http://127.0.0.1:5000
- AI 助手成功初始化（非模拟模式）
- 模型加载状态：
  - LLM 模型：Qwen2.5-3B-Instruct (CUDA, INT4 量化)
  - 向量模型：BGE-M3 (CPU)
  - 显存占用：2.49 GB
- 功能模块：
  - ✅ 聊天功能
  - ✅ 翻译功能 - **占位符处理机制全面升级**
  - ✅ 知识库 (103 个条目) - **内容预览正常显示**
  - ✅ 术语库 (11 个术语) - **支持多术语和反向映射**
  - ✅ 语音功能 (需要安装 modelscope)
  - ✅ 设置管理

### 📋 修复详情

**知识库API修复要点：**
- 使用条目名称（如 `操作界面问答.txt_QA_1`）作为唯一ID
- 支持路径参数中的特殊字符（使用 `<path:item_id>` 路由）
- 统一PC端和WEB端的数据访问方式
- 保持问答组、文档片段等不同类型条目的正确显示格式
- 测试验证：所有API返回200状态码，无404错误

### 技术架构

**桌面端与 WEB 端统一架构：**
- 核心 AI 引擎共享
- 模型管理统一
- 数据存储一致
- 功能接口标准化

## 项目概述

本项目是一个高度模块化、可扩展的VLLM应用开发框架，包含以下核心功能：

- **模型加载与管理**：支持加载各种格式的大语言模型，包括Transformers模型和GGUF模型
- **向量数据库**：提供高效的文本向量存储和检索功能
- **知识库管理**：支持知识的导入、存储、检索和管理
- **应用打包**：支持将应用打包为可执行文件
- **版本管理**：提供版本控制和自动更新功能
- **资源打包**：支持将模型、数据等资源打包到应用中

## 系统架构

```
├── core/                    # 核心功能模块
│   ├── base_engine.py       # 基础引擎，提供模型加载等基础功能
│   ├── vector_db.py         # 向量数据库，提供文本向量存储和检索
│   ├── knowledge_base.py    # 知识库管理，提供知识的存储和检索
│   └── knowledge_engine.py  # 知识问答引擎，提供基于知识的问答功能
├── utils/                   # 工具函数模块
├── ui/                      # 用户界面模块
├── data/                    # 数据存储目录
├── config/                  # 配置文件目录
├── version_manager.py       # 版本管理工具
├── auto_updater.py          # 自动更新工具
├── build_exe.py             # 应用打包工具
└── AI_assistant.py          # 主应用程序
```

## 核心模块说明

### 1. 基础引擎 (base_engine.py)

基础引擎提供对大语言模型的加载和管理功能，主要特点：

- 支持多种模型格式（Transformers和GGUF）
- 自动优化模型加载参数，支持GPU加速
- 提供统一的模型推理接口

### 2. 向量数据库 (vector_db.py)

向量数据库负责文本向量的存储和检索，主要特点：

- 支持多种向量集合管理
- 高效的相似度搜索算法
- 支持向量持久化存储和加载

### 3. 知识库 (knowledge_base.py)

知识库模块管理结构化和非结构化知识，主要特点：

- 支持多种格式的知识导入（文本、PDF等）
- 自动文档解析和切分
- 基于向量数据库的高效检索

### 4. 知识问答引擎 (knowledge_engine.py)

知识问答引擎提供基于知识的问答功能，主要特点：

- 基于检索增强生成（RAG）技术
- 支持上下文管理和多轮对话
- 结果验证和质量控制

## 工具和集成

### 版本管理 (version_manager.py)

- 提供应用版本管理功能
- 支持远程版本检测和比较
- 生成增量更新包

### 自动更新 (auto_updater.py)

- 自动检测新版本
- 下载和应用更新
- 支持增量更新和完整更新

### 应用打包 (build_exe.py)

- 将应用打包为独立可执行文件
- 资源文件打包和管理
- 依赖项管理

## 快速开始

### 环境要求

- Python 3.8+
- CUDA 11.7+（如需GPU加速）
- 至少8GB内存，推荐16GB以上

### 安装步骤

1. 克隆仓库
```bash
git clone https://github.com/yourusername/vllm-application-framework.git
cd vllm-application-framework
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 创建数据目录
```bash
python create_data_dirs.py
```

4. 运行应用
```bash
python AI_assistant.py
```

## 开发指南

### 自定义模型

修改`config.json`文件中的`model_path`参数指向你的模型路径：

```json
{
  "model_path": "path/to/your/model",
  "temperature": 0.7,
  "max_new_tokens": 512
}
```

### 扩展知识库

通过代码或UI导入知识到知识库：

```python
from core.knowledge_base import KnowledgeBase

kb = KnowledgeBase()
kb.import_file("path/to/document.pdf")
```

### 创建自定义应用

继承核心模块创建自定义应用：

```python
from core.base_engine import BaseEngine
from core.knowledge_base import KnowledgeBase

class MyCustomApp:
    def __init__(self):
        self.engine = BaseEngine({"model_path": "path/to/model"})
        self.kb = KnowledgeBase()

    def run(self):
        # 自定义应用逻辑
        pass
```

## 技术栈

- **PyTorch**: 深度学习框架
- **Transformers**: 模型加载和推理
- **GGUF**: 轻量级模型格式支持
- **NumPy**: 数学计算
- **FastAPI**: API服务（可选）

## 贡献指南

1. Fork 仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 许可证

本项目采用 MIT 许可证 - 详情见 [LICENSE](LICENSE) 文件

## 联系方式

- 项目负责人: Your Name
- 邮箱: <EMAIL>
- 项目主页: https://github.com/yourusername/vllm-application-framework

# 松瓷机电AI助手 - 自动模型量化功能

## 功能简介

松瓷机电AI助手支持根据当前GPU资源自动选择模型量化级别，以便在各种硬件环境下获得最佳性能。量化可以减少模型所需的显存和内存，同时保持模型的推理能力。

## 核心功能特性

- **自动显存检测**：自动检测可用GPU显存，并根据实际情况选择合适的量化级别
- **多级量化支持**：支持FP16、INT8和INT4多种量化级别
- **特定模型优化**：针对Qwen等模型提供特定优化配置
- **内存使用优化**：提供显存动态分配和CPU卸载功能
- **可视化设置界面**：在设置面板中可直观地调整量化配置

## 量化级别说明

| 级别 | 描述 | 建议显存 | 性能影响 |
|------|------|----------|----------|
| NONE | 原始精度，不进行量化 | ≥24GB | 最佳性能，原始精度 |
| FP16 | 半精度浮点数 | ≥12GB | 轻微影响，几乎无精度损失 |
| INT8 | 8位整数量化 | ≥8GB | 中等影响，可能有少量精度损失 |
| INT4 | 4位整数量化 | ≥4GB | 显著影响，有一定精度损失 |

## 使用方法

1. 在设置面板中的"模型量化设置"组中选择量化选项
2. 可以选择"自动选择"让系统根据GPU资源自动决定量化级别
3. 如有特殊需要，可以手动选择特定的量化级别
4. 设置在下次加载模型时生效

## 技术实现

量化功能通过以下核心组件实现：

- **ModelQuantizer**：负责检测GPU资源并自动选择量化级别
- **BitsAndBytesConfig**：利用transformers的量化配置功能
- **显存管理**：根据不同硬件环境优化显存分配

## 支持的模型

目前自动量化功能已针对以下模型类型进行了优化：

- Qwen系列模型 (Qwen2-0.5B, Qwen2-1.5B, Qwen2-7B, Qwen1.5等)
- LLaMA系列模型
- 通用Transformer模型

## 注意事项

- INT4量化可能会对模型性能产生一定影响，主要体现在生成文本的质量和一致性上
- 使用较高级别的量化可能导致模型在某些知识密集型任务上表现降低
- 对于重要任务，建议在资源允许的情况下使用较低级别的量化或不进行量化

## 未来改进计划

- 添加更多模型专用优化配置
- 支持更多量化方法 (GPTQ, EETQ等)
- 动态运行时量化调整
- 性能监控和自动调整功能

# 松瓷机电AI助手一体化打包工具

## 简介

松瓷机电AI助手一体化打包工具是一套为AI应用程序设计的打包解决方案，能够将松瓷机电AI助手应用与其环境（包括Python运行时、依赖库、模型文件等）一起打包成独立可执行文件或安装程序，便于分发和部署。

该工具集包含以下组件：

- **build_bundle.bat**: Windows批处理脚本，提供交互式界面，简化打包过程
- **package_app.py**: 基础打包脚本，适用于普通Python虚拟环境
- **package_conda_app.py**: 专为Conda环境设计的打包脚本，可捕获Conda环境中的依赖

## 功能特点

- 自动检测和捕获依赖关系
- 支持标准Python环境和Conda环境
- 可选择性包含大型模型文件
- 自动创建Windows安装程序
- 保留应用程序目录结构
- 提供简洁的命令行和交互式界面
- 生成详细的打包日志

## 系统要求

- Windows 10/11 (64位)
- Python 3.8+
- PyInstaller
- NSIS（可选，用于创建安装程序）

## 快速开始

### 使用交互式批处理脚本（推荐）

1. 双击运行 `build_bundle.bat`
2. 按照提示设置应用名称、版本号等选项
3. 确认设置并开始打包
4. 打包完成后，可执行文件将位于 `dist` 目录中

### 使用命令行（Python环境）

```bash
# 基本用法
python package_app.py

# 自定义应用名称和版本
python package_app.py --name "我的松瓷机电AI助手" --version "2.0.0"

# 包含模型文件
python package_app.py --include-models

# 不创建安装程序
python package_app.py --no-installer
```

### 使用命令行（Conda环境）

```bash
# 基本用法
python package_conda_app.py --conda-env myenv

# 自定义应用名称和版本，指定Conda环境
python package_conda_app.py --name "我的松瓷机电AI助手" --version "2.0.0" --conda-env myenv

# 包含模型文件
python package_conda_app.py --conda-env myenv --include-models
```

## 命令行参数

所有脚本支持以下通用参数：

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--name` | 应用程序名称 | "松瓷机电AI助手" |
| `--version` | 应用程序版本 | "1.0.0" |
| `--main` | 主脚本路径 | "AI_assistant.py" |
| `--output` | 输出目录 | "dist" |
| `--include-models` | 包含模型文件（大幅增加包体积） | False |
| `--no-installer` | 不创建安装程序 | False |

针对Conda环境的额外参数：

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--conda-env` | 指定Conda环境名称 | "vllm_env" |

## 打包流程

1. **环境检测**：检查Python/Conda环境，确保必要的依赖已安装
2. **依赖收集**：收集应用程序的依赖项
3. **数据文件收集**：收集配置文件、资源、模型文件等
4. **生成规格文件**：创建PyInstaller规格文件
5. **构建可执行文件**：使用PyInstaller构建独立可执行文件
6. **创建安装程序**：使用NSIS创建Windows安装程序（可选）

## 目录结构

打包后的应用程序将保留原有目录结构：

```
松瓷机电AI助手/
├── 松瓷机电AI助手.exe        # 主可执行文件
├── app_info.json     # 应用程序信息
├── config.json       # 配置文件
├── README.md         # 说明文档
├── data/             # 数据目录
│   ├── knowledge/    # 知识库
│   ├── terms/        # 术语库
│   └── vectors/      # 向量存储
├── models/           # 模型文件（如果包含）
└── python-XXX/       # Python运行时
```

## 注意事项

1. 打包过程可能需要较长时间，特别是包含模型文件时
2. 打包后的应用体积较大，建议在分发前进行测试
3. 对于特别大的模型文件（如完整的LLM模型），建议单独分发
4. 部分依赖可能需要特殊处理，请参阅日志输出
5. 创建安装程序需要安装NSIS并将其添加到PATH环境变量

## 常见问题

**Q: 打包失败，出现"No module named XXX"错误**
A: 这通常是由于某些隐藏依赖未被正确捕获。请检查日志，手动将缺失的依赖添加到requirements.txt中，然后重新打包。

**Q: 打包成功但运行时报错**
A: 可能是由于某些资源文件路径问题。请检查应用程序是否正确处理了资源文件路径。使用相对路径或应用内部路径解析机制。

**Q: 打包后的文件太大**
A: 考虑以下措施减小体积：
- 不包含模型文件，改为运行时下载
- 使用更轻量级的依赖库
- 清理不必要的数据文件

**Q: 在非Windows系统上使用**
A: package_app.py 和 package_conda_app.py 支持在Linux和macOS上运行，但创建安装程序功能仅适用于Windows。

# WEB UI 界面模块

## 概述

WEB UI模块为松瓷机电AI助手提供基于浏览器的用户界面，支持局域网访问，功能与PC端UI应用完全一致。该模块基于Flask框架构建，提供RESTful API和WebSocket实时通信。

## 核心特性

- **完整功能对等**：与PC端UI功能完全一致，包括聊天、翻译、知识库管理、设置等
- **响应式设计**：支持桌面、平板、手机等多种设备访问
- **局域网支持**：可配置为局域网服务，支持多用户同时访问
- **实时通信**：基于WebSocket的实时消息推送
- **安全访问**：支持用户认证和权限管理

## 系统架构

```
web_ui/
├── app.py                  # Flask主应用
├── api/                    # API接口模块
│   ├── __init__.py
│   ├── chat_api.py         # 聊天API
│   ├── translation_api.py  # 翻译API
│   ├── knowledge_api.py    # 知识库API
│   ├── settings_api.py     # 设置API
│   └── voice_api.py        # 语音API
├── static/                 # 静态资源
│   ├── css/               # 样式文件
│   ├── js/                # JavaScript文件
│   └── assets/            # 图片等资源
├── templates/             # HTML模板
│   ├── base.html          # 基础模板
│   ├── index.html         # 主页面
│   ├── chat.html          # 聊天界面
│   ├── translation.html   # 翻译界面
│   ├── knowledge.html     # 知识库界面
│   └── settings.html      # 设置界面
├── websocket/             # WebSocket处理
│   ├── __init__.py
│   └── handlers.py        # WebSocket事件处理
└── config/                # WEB配置
    ├── __init__.py
    └── web_config.py      # WEB服务配置
```

## 功能模块

### 1. 聊天模块 (Chat)
- 实时对话界面
- 消息历史记录
- 多轮对话支持
- 流式响应显示

### 2. 翻译模块 (Translation)
- 文本翻译功能
- 支持多种语言
- 术语库集成
- 翻译历史记录

### 3. 知识库模块 (Knowledge)
- 知识条目管理
- 文档导入功能
- 知识检索和搜索
- 向量化管理

### 4. 设置模块 (Settings)
- 模型配置管理
- 系统参数设置
- 用户偏好配置
- 性能监控

### 5. 语音模块 (Voice)
- 语音合成功能
- 语音识别支持
- 自定义语音训练
- 音频文件管理

## 技术栈

- **后端框架**：Flask + Flask-SocketIO
- **前端技术**：HTML5 + CSS3 + JavaScript (ES6+)
- **UI框架**：Bootstrap 5 + Font Awesome
- **实时通信**：WebSocket (Socket.IO)
- **API设计**：RESTful API
- **数据格式**：JSON

## 部署配置

### 开发环境启动

```bash
# 安装WEB依赖
pip install flask flask-socketio flask-cors

# 启动WEB服务
python web_ui/app.py
```

### 局域网配置

在 `web_ui/config/web_config.py` 中配置：

```python
# 服务器配置
HOST = '0.0.0.0'  # 监听所有网络接口
PORT = 5000       # 服务端口
DEBUG = False     # 生产环境关闭调试

# 安全配置
SECRET_KEY = 'your-secret-key'
ENABLE_AUTH = True  # 启用用户认证
```

## 使用指南

### 启动WEB服务

1. **启动松瓷机电AI助手WEB服务**：
   ```bash
   python web_ui/app.py
   ```

2. **访问界面**：
   - 本地访问：http://localhost:5000
   - 局域网访问：http://[服务器IP]:5000

### 功能使用

#### 聊天功能
- 支持实时对话，与PC端体验一致
- 可开启/关闭知识库问答功能
- 支持多轮对话和上下文理解
- 消息历史自动保存

#### 知识库管理
- **浏览知识库**：在知识库页面查看所有知识条目
- **内容预览**：点击条目可预览详细内容
- **搜索功能**：支持关键词搜索知识条目
- **问答功能**：在聊天中开启知识库问答开关

#### 翻译功能
- 支持中英文互译
- 集成术语库，确保翻译一致性
- 翻译历史记录

#### 设置管理
- 模型参数配置
- 系统性能监控
- 用户偏好设置

### 移动端使用

WEB界面采用响应式设计，完美支持移动设备：
- 手机浏览器直接访问
- 平板设备优化显示
- 触摸操作友好

### 多用户支持

- 支持多个用户同时访问
- 每个用户独立的会话管理
- 共享知识库和模型资源
```

### 访问方式

- 本地访问：http://localhost:5000
- 局域网访问：http://[服务器IP]:5000

## API接口文档

### 聊天API
- `POST /api/chat/send` - 发送消息
- `GET /api/chat/history` - 获取历史记录
- `DELETE /api/chat/clear` - 清空历史

### 翻译API
- `POST /api/translation/translate` - 执行翻译
- `GET /api/translation/history` - 翻译历史
- `GET /api/translation/languages` - 支持语言列表

### 知识库API
- `GET /api/knowledge/items` - 获取知识条目
- `POST /api/knowledge/add` - 添加知识条目
- `PUT /api/knowledge/update` - 更新知识条目
- `DELETE /api/knowledge/delete` - 删除知识条目
- `POST /api/knowledge/search` - 搜索知识

### 设置API
- `GET /api/settings/config` - 获取配置
- `POST /api/settings/update` - 更新配置
- `GET /api/settings/models` - 获取模型列表

## 安全考虑

- **用户认证**：支持基本认证和会话管理
- **CORS配置**：合理配置跨域访问策略
- **输入验证**：严格验证所有用户输入
- **访问控制**：基于角色的权限管理

## 性能优化

- **静态资源缓存**：合理设置静态资源缓存策略
- **API响应优化**：使用分页和数据压缩
- **WebSocket连接管理**：优化连接池和消息队列
- **前端优化**：代码压缩和懒加载

## 扩展功能

- **多语言支持**：国际化界面
- **主题定制**：支持多种UI主题
- **插件系统**：支持功能插件扩展
- **移动端适配**：PWA支持

## 维护和监控

- **日志记录**：详细的访问和错误日志
- **性能监控**：API响应时间和资源使用监控
- **健康检查**：服务状态监控接口
- **自动重启**：异常情况下的自动恢复机制

## 更新日志

### 版本 1.1.0 (2025-01-XX)
#### WEB界面优化
- **知识库显示优化**: 修复WEB页面知识库条目显示问题，现在正确显示知识点内容预览而非文件名
- **主页功能扩展**: 新增术语库管理快捷访问卡片，提供完整的功能导航
- **智能对话增强**:
  - 新增知识库问答开关，支持在知识库问答和普通对话模式间切换
  - 非知识库问答模式下默认支持多轮对话
  - 优化对话体验和响应速度

#### 知识库兼容性提升
- **多格式文档支持**:
  - 兼容标准问答对文档的向量化及显示
  - 新增对非标准问答对文档的支持，自动分块处理
  - 智能识别文档类型，采用相应的处理策略
- **显示优化**:
  - 问答对类型：以问题为标题，结构化显示问答内容
  - 文档片段类型：显示片段索引和来源信息
  - 普通文本类型：保持原有显示方式

#### 技术改进
- 优化模型加载机制，避免重复初始化造成的资源浪费
- 改进WebSocket通信，支持知识库问答模式参数传递
- 增强错误处理和用户反馈机制

### 版本 1.0.0 (2024-12-XX)
- 初始版本发布
- 支持基本的AI对话功能
- 集成知识库和向量搜索
- 支持多语言翻译
- 提供桌面GUI界面