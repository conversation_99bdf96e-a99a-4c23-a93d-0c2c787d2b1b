# 松瓷机电AI助手 WEB UI 新功能演示

## 功能概述

本次更新为松瓷机电AI助手WEB界面添加了两个重要功能：

1. **智能GPU资源管理** - 在切换模型时自动优化GPU使用
2. **思维链折叠显示** - 支持AI回答中思维链的折叠显示和纯净复制

## 1. 智能GPU资源管理

### 功能说明

当用户在WEB界面中切换不同类型的AI模型时，系统会自动管理GPU资源：

- **切换到Ollama/OpenAI模型**：自动卸载本地LLM模型，释放GPU内存
- **切换回本地模型**：自动重新加载本地LLM模型
- **同类型模型切换**：无需额外处理，保持当前状态

### 技术实现

```python
def _manage_model_resources(selected_model):
    """管理模型资源，在切换模型时优化GPU使用"""
    global current_model_type, local_model_loaded
    
    # 确定新模型类型
    new_model_type = MODEL_TYPE_LOCAL
    if selected_model.startswith('openai_'):
        new_model_type = MODEL_TYPE_OPENAI
    elif selected_model.startswith('ollama_'):
        new_model_type = MODEL_TYPE_OLLAMA
        
    # 如果切换到非本地模型，卸载本地模型
    if current_model_type == MODEL_TYPE_LOCAL and new_model_type != MODEL_TYPE_LOCAL:
        if local_model_loaded:
            assistant.unload_local_model()
            local_model_loaded = False
            
    # 如果切换回本地模型，重新加载
    elif current_model_type != MODEL_TYPE_LOCAL and new_model_type == MODEL_TYPE_LOCAL:
        if not local_model_loaded:
            success = assistant.reload_local_model()
            local_model_loaded = success
```

### 使用场景

1. **资源受限环境**：在GPU内存有限的情况下，避免同时加载多个模型
2. **多模型测试**：方便用户在不同模型间切换进行对比测试
3. **性能优化**：减少不必要的内存占用，提高系统响应速度

## 2. 思维链折叠显示

### 功能说明

AI模型（如DeepSeek-R1）在回答问题时会产生思维链（thinking chain），现在支持：

- **自动检测**：识别回答中的 `<think>...</think>` 标签
- **折叠显示**：思维链默认折叠，用户可点击展开
- **纯净复制**：复制功能只复制最终答案，不包含思维链

### 思维链示例

**原始AI回答：**
```
<think>
用户问的是关于Python的问题。我需要：
1. 分析问题的核心
2. 提供清晰的解答
3. 给出示例代码
</think>

关于Python编程，我可以为您提供以下建议：

Python是一种简洁而强大的编程语言...
```

**处理后显示：**
```html
<div class="thinking-chain-container">
    <div class="thinking-toggle" onclick="toggleThinking(0)">
        <i class="fas fa-brain"></i> 思维过程 
        <i class="fas fa-chevron-down toggle-icon"></i>
    </div>
    <div class="thinking-content collapsed" id="thinking-0">
        <div class="thinking-text">
            用户问的是关于Python的问题。我需要：
            1. 分析问题的核心
            2. 提供清晰的解答
            3. 给出示例代码
        </div>
    </div>
</div>

关于Python编程，我可以为您提供以下建议：

Python是一种简洁而强大的编程语言...
```

### CSS样式设计

```css
.thinking-chain-container {
    margin: 0.75rem 0;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    background-color: #f8f9fa;
}

.thinking-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    background-color: #e9ecef;
    transition: var(--transition);
}

.thinking-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.thinking-content.expanded {
    max-height: 500px;
    padding: 0.75rem;
}
```

### JavaScript交互

```javascript
function toggleThinking(thinkingId) {
    const toggle = document.querySelector(`[onclick="toggleThinking(${thinkingId})"]`);
    const content = document.getElementById(`thinking-${thinkingId}`);
    
    const isExpanded = content.classList.contains('expanded');
    
    if (isExpanded) {
        content.classList.remove('expanded');
        content.classList.add('collapsed');
    } else {
        content.classList.remove('collapsed');
        content.classList.add('expanded');
    }
}

function copyCleanContent(button) {
    const messageContent = button.closest('.message-content');
    const clonedContent = messageContent.cloneNode(true);
    
    // 移除思维链容器
    const thinkingContainers = clonedContent.querySelectorAll('.thinking-chain-container');
    thinkingContainers.forEach(container => container.remove());
    
    // 获取纯净文本
    let cleanText = clonedContent.textContent || clonedContent.innerText || '';
    cleanText = cleanText.replace(/\n\s*\n/g, '\n').trim();
    
    // 复制到剪贴板
    navigator.clipboard.writeText(cleanText);
}
```

## 3. 使用指南

### 启动WEB服务

```bash
cd d:\AI_project\vllm_模型应用
python web_ui/app.py
```

### 访问界面

打开浏览器访问：http://127.0.0.1:5000

### 测试新功能

1. **测试模型切换**：
   - 在聊天界面选择不同类型的模型
   - 观察控制台日志中的资源管理信息
   - 检查GPU内存使用情况

2. **测试思维链显示**：
   - 使用支持思维链的模型（如DeepSeek-R1）
   - 发送问题并观察回答中的思维链显示
   - 点击思维链标题测试折叠/展开功能
   - 使用复制按钮测试纯净内容复制

## 4. 技术特点

### 资源管理优势

- **内存优化**：避免重复加载模型，节省GPU内存
- **智能切换**：根据模型类型自动决定是否需要卸载/重载
- **状态跟踪**：实时跟踪模型加载状态，避免重复操作

### 用户体验提升

- **清晰展示**：思维链与最终答案分离显示
- **交互友好**：支持点击展开/折叠，提供视觉反馈
- **复制便利**：一键复制纯净答案，无需手动筛选

### 兼容性保证

- **向后兼容**：不影响现有功能的正常使用
- **渐进增强**：新功能作为增强特性，不破坏原有体验
- **错误处理**：完善的异常处理，确保系统稳定性

## 5. 未来扩展

### 计划中的改进

1. **更多模型支持**：扩展对更多AI模型平台的支持
2. **资源监控**：添加实时GPU/内存使用监控界面
3. **思维链分析**：提供思维链质量分析和统计功能
4. **个性化设置**：允许用户自定义思维链显示偏好

### 性能优化

1. **缓存机制**：优化模型加载速度
2. **异步处理**：改进模型切换的响应时间
3. **内存管理**：更精细的内存使用控制

---

**更新时间**：2025-01-27  
**版本**：v1.2.0  
**开发者**：松瓷机电AI助手开发团队
