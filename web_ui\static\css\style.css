/* 松瓷机电AI助手WEB UI 自定义样式 */

/* 全局样式 */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;

    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

    --transition: all 0.15s ease-in-out;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

.navbar-nav .nav-link.active {
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
}

/* 按钮样式 */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 聊天界面样式 */
.chat-container {
    height: 70vh;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background-color: #fff;
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
}

.message {
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
}

.message.user {
    justify-content: flex-end;
}

.message.assistant {
    justify-content: flex-start;
}

.message-content {
    max-width: 70%;
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    word-wrap: break-word;
}

.message.user .message-content {
    background-color: var(--primary-color);
    color: white;
    border-bottom-right-radius: 0.25rem;
}

.message.assistant .message-content {
    background-color: #e9ecef;
    color: var(--dark-color);
    border-bottom-left-radius: 0.25rem;
}

.message-time {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.message.user .message-time {
    text-align: right;
}

.message.assistant .message-time {
    text-align: left;
}

/* 思考中动画 */
.thinking-indicator {
    display: inline-flex;
    align-items: center;
}

.thinking-dots {
    display: inline-flex;
    margin-left: 0.5rem;
}

.thinking-dots span {
    width: 0.5rem;
    height: 0.5rem;
    background-color: #6c757d;
    border-radius: 50%;
    margin: 0 0.1rem;
    animation: thinking 1.4s infinite ease-in-out both;
}

.thinking-dots span:nth-child(1) { animation-delay: -0.32s; }
.thinking-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes thinking {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* Markdown内容样式 */
.message-text {
    line-height: 1.6;
}

.message-text h1, .message-text h2, .message-text h3,
.message-text h4, .message-text h5, .message-text h6 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.message-text h1 { font-size: 1.5rem; }
.message-text h2 { font-size: 1.3rem; }
.message-text h3 { font-size: 1.1rem; }
.message-text h4, .message-text h5, .message-text h6 { font-size: 1rem; }

.message-text p {
    margin-bottom: 0.75rem;
}

.message-text ul, .message-text ol {
    margin-bottom: 0.75rem;
    padding-left: 1.5rem;
}

.message-text li {
    margin-bottom: 0.25rem;
}

.message-text blockquote {
    border-left: 4px solid #dee2e6;
    padding-left: 1rem;
    margin: 0.75rem 0;
    font-style: italic;
    color: #6c757d;
}

.message-text code {
    background-color: #f8f9fa;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875em;
}

.message-text pre {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin: 0.75rem 0;
    overflow-x: auto;
}

.message-text pre code {
    background-color: transparent;
    padding: 0;
    border-radius: 0;
}

.message-text table {
    width: 100%;
    border-collapse: collapse;
    margin: 0.75rem 0;
}

.message-text th, .message-text td {
    border: 1px solid #dee2e6;
    padding: 0.5rem;
    text-align: left;
}

.message-text th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.message-text a {
    color: var(--primary-color);
    text-decoration: none;
}

.message-text a:hover {
    text-decoration: underline;
}

/* 知识库标记 */
.knowledge-badge {
    display: inline-flex;
    align-items: center;
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 0.5rem;
    margin-right: 0.5rem;
}

.knowledge-badge i {
    margin-right: 0.25rem;
}

/* 模型标记 */
.model-badge {
    display: inline-flex;
    align-items: center;
    background-color: rgba(0, 123, 255, 0.1);
    color: var(--primary-color);
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 0.5rem;
}

.model-badge i {
    margin-right: 0.25rem;
}

/* 思维链折叠样式 */
.thinking-chain-container {
    margin: 0.75rem 0;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    background-color: #f8f9fa;
}

.thinking-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    background-color: #e9ecef;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-size: 0.875rem;
    font-weight: 500;
    color: #495057;
    user-select: none;
}

.thinking-toggle:hover {
    background-color: #dee2e6;
}

.thinking-toggle i.fas.fa-brain {
    color: #6f42c1;
    margin-right: 0.5rem;
}

.thinking-toggle .toggle-icon {
    transition: transform 0.3s ease;
    font-size: 0.75rem;
}

.thinking-toggle.expanded .toggle-icon {
    transform: rotate(180deg);
}

.thinking-content {
    padding: 0.75rem;
    background-color: #fff;
    border-top: 1px solid #e9ecef;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.thinking-content.collapsed {
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
}

.thinking-content.expanded {
    max-height: 500px;
    padding: 0.75rem;
}

.thinking-text {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #495057;
    white-space: pre-wrap;
    word-wrap: break-word;
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    border-left: 3px solid #6f42c1;
}

/* 复制按钮样式 */
.copy-button {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    color: #6c757d;
    cursor: pointer;
    transition: var(--transition);
    opacity: 0;
}

.message-content:hover .copy-button {
    opacity: 1;
}

.copy-button:hover {
    background: rgba(0, 0, 0, 0.2);
    color: #495057;
}

.copy-button.copied {
    background: var(--success-color);
    color: white;
}

/* 模型选择器样式 */
#model-selector {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
}

#model-selector:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

#model-selector option {
    background-color: var(--primary-color);
    color: white;
}

/* 翻译界面样式 */
.translation-container {
    min-height: 60vh;
}

.translation-panel {
    background-color: #fff;
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    padding: 1.5rem;
}

.language-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1rem 0;
}

.language-swap {
    margin: 0 1rem;
    cursor: pointer;
    color: var(--primary-color);
    transition: var(--transition);
}

.language-swap:hover {
    color: var(--dark-color);
    transform: rotate(180deg);
}

/* 知识库界面样式 */
.knowledge-sidebar {
    background-color: #fff;
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    max-height: 70vh;
    overflow-y: auto;
}

.knowledge-item {
    padding: 0.75rem;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    transition: var(--transition);
}

.knowledge-item:hover {
    background-color: #f8f9fa;
}

.knowledge-item.active {
    background-color: var(--primary-color);
    color: white;
}

.knowledge-content {
    background-color: #fff;
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    padding: 1.5rem;
    min-height: 60vh;
}

/* 设置界面样式 */
.settings-section {
    background-color: #fff;
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.settings-section h5 {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

/* 语音界面样式 */
.voice-controls {
    text-align: center;
    padding: 2rem;
}

.voice-button {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: none;
    font-size: 2rem;
    transition: var(--transition);
    margin: 0.5rem;
}

.voice-button.recording {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: white;
}

/* 状态指示器 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
}

.status-indicator.online {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.status-indicator.offline {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.status-indicator.connecting {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

/* 进度条样式 */
.progress {
    height: 0.5rem;
    border-radius: var(--border-radius);
}

.progress-bar {
    transition: width 0.3s ease;
}

/* 文件上传区域 */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(0, 123, 255, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(0, 123, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chat-container {
        height: 60vh;
    }

    .message-content {
        max-width: 85%;
    }

    .language-selector {
        flex-direction: column;
    }

    .language-swap {
        margin: 0.5rem 0;
        transform: rotate(90deg);
    }

    .voice-button {
        width: 80px;
        height: 80px;
        font-size: 1.5rem;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: var(--border-radius);
}
