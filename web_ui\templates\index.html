{% extends "base.html" %}

{% block title %}松瓷机电AI助手 - 主页{% endblock %}

{% block content %}
<div class="row">
    <!-- 欢迎区域 -->
    <div class="col-12 mb-4">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body text-center py-5">
                <h1 class="display-4 mb-3">
                    <i class="fas fa-robot me-3"></i>松瓷机电AI助手
                </h1>
                <p class="lead mb-4">智能对话、翻译、知识管理一体化平台</p>
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <p class="mb-0">欢迎使用松瓷机电AI助手WEB版！这里提供与桌面版完全相同的功能，支持智能对话、多语言翻译、知识库管理等。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能模块卡片 -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 shadow-sm hover-card">
            <div class="card-body text-center">
                <div class="feature-icon mb-3">
                    <i class="fas fa-comments fa-3x text-primary"></i>
                </div>
                <h5 class="card-title">智能对话</h5>
                <p class="card-text">与松瓷机电AI助手进行自然对话，获得智能回答和建议。支持多轮对话和上下文理解。</p>
                <a href="{{ url_for('chat') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-1"></i>开始聊天
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 shadow-sm hover-card">
            <div class="card-body text-center">
                <div class="feature-icon mb-3">
                    <i class="fas fa-language fa-3x text-success"></i>
                </div>
                <h5 class="card-title">多语言翻译</h5>
                <p class="card-text">支持多种语言之间的智能翻译，集成术语库确保翻译的准确性和一致性。</p>
                <a href="{{ url_for('translation') }}" class="btn btn-success">
                    <i class="fas fa-arrow-right me-1"></i>开始翻译
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 shadow-sm hover-card">
            <div class="card-body text-center">
                <div class="feature-icon mb-3">
                    <i class="fas fa-book fa-3x text-info"></i>
                </div>
                <h5 class="card-title">知识库管理</h5>
                <p class="card-text">管理和检索知识条目，支持文档导入、向量化搜索和智能问答。</p>
                <a href="{{ url_for('knowledge') }}" class="btn btn-info">
                    <i class="fas fa-arrow-right me-1"></i>管理知识
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 shadow-sm hover-card">
            <div class="card-body text-center">
                <div class="feature-icon mb-3">
                    <i class="fas fa-book-open fa-3x text-purple"></i>
                </div>
                <h5 class="card-title">术语库管理</h5>
                <p class="card-text">管理专业术语对照表，提升翻译质量和一致性，支持多语言术语管理。</p>
                <a href="{{ url_for('terminology') }}" class="btn btn-purple">
                    <i class="fas fa-arrow-right me-1"></i>管理术语
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 shadow-sm hover-card">
            <div class="card-body text-center">
                <div class="feature-icon mb-3">
                    <i class="fas fa-microphone fa-3x text-warning"></i>
                </div>
                <h5 class="card-title">语音功能</h5>
                <p class="card-text">语音合成和识别功能，支持文本转语音、语音转文本和自定义语音训练。</p>
                <a href="{{ url_for('voice') }}" class="btn btn-warning">
                    <i class="fas fa-arrow-right me-1"></i>语音功能
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 shadow-sm hover-card">
            <div class="card-body text-center">
                <div class="feature-icon mb-3">
                    <i class="fas fa-cog fa-3x text-secondary"></i>
                </div>
                <h5 class="card-title">系统设置</h5>
                <p class="card-text">配置AI模型参数、系统设置和用户偏好，监控系统状态和性能。</p>
                <a href="{{ url_for('settings') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i>系统设置
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 shadow-sm hover-card">
            <div class="card-body text-center">
                <div class="feature-icon mb-3">
                    <i class="fas fa-chart-line fa-3x text-danger"></i>
                </div>
                <h5 class="card-title">系统状态</h5>
                <p class="card-text">实时监控系统运行状态、资源使用情况和各组件的健康状态。</p>
                <button class="btn btn-danger" onclick="showSystemStatus()">
                    <i class="fas fa-arrow-right me-1"></i>查看状态
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 系统状态模态框 -->
<div class="modal fade" id="systemStatusModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-line me-2"></i>系统状态
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="system-status-content">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在获取系统状态...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="refreshSystemStatus()">
                    <i class="fas fa-sync-alt me-1"></i>刷新
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.hover-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.feature-icon {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.text-purple {
    color: #6f42c1 !important;
}

.btn-purple {
    background-color: #6f42c1;
    border-color: #6f42c1;
    color: white;
}

.btn-purple:hover {
    background-color: #5a359a;
    border-color: #5a359a;
    color: white;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function showSystemStatus() {
    const modal = new bootstrap.Modal(document.getElementById('systemStatusModal'));
    modal.show();
    loadSystemStatus();
}

function loadSystemStatus() {
    fetch('/api/settings/system_info')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displaySystemStatus(data.system_info);
            } else {
                document.getElementById('system-status-content').innerHTML =
                    '<div class="alert alert-danger">获取系统状态失败: ' + (data.error || '未知错误') + '</div>';
            }
        })
        .catch(error => {
            document.getElementById('system-status-content').innerHTML =
                '<div class="alert alert-danger">网络错误: ' + error.message + '</div>';
        });
}

function displaySystemStatus(status) {
    const content = document.getElementById('system-status-content');

    let html = '<div class="row">';

    // 系统信息
    html += '<div class="col-md-6 mb-3">';
    html += '<h6><i class="fas fa-desktop me-2"></i>系统信息</h6>';
    html += '<ul class="list-unstyled">';
    html += '<li><strong>操作系统:</strong> ' + status.platform.system + ' ' + status.platform.release + '</li>';
    html += '<li><strong>架构:</strong> ' + status.platform.machine + '</li>';
    html += '<li><strong>处理器:</strong> ' + (status.platform.processor || 'Unknown') + '</li>';
    html += '</ul>';
    html += '</div>';

    // 资源使用
    html += '<div class="col-md-6 mb-3">';
    html += '<h6><i class="fas fa-memory me-2"></i>资源使用</h6>';
    html += '<ul class="list-unstyled">';
    html += '<li><strong>CPU使用率:</strong> ' + status.cpu.percent.toFixed(1) + '%</li>';
    html += '<li><strong>内存使用率:</strong> ' + status.memory.percent.toFixed(1) + '%</li>';
    html += '<li><strong>可用内存:</strong> ' + (status.memory.available / 1024 / 1024 / 1024).toFixed(1) + ' GB</li>';
    html += '</ul>';
    html += '</div>';

    // GPU信息
    if (status.gpu.available) {
        html += '<div class="col-md-6 mb-3">';
        html += '<h6><i class="fas fa-microchip me-2"></i>GPU信息</h6>';
        html += '<ul class="list-unstyled">';
        html += '<li><strong>GPU数量:</strong> ' + status.gpu.count + '</li>';
        status.gpu.devices.forEach((gpu, index) => {
            html += '<li><strong>GPU ' + index + ':</strong> ' + gpu.name + '</li>';
        });
        html += '</ul>';
        html += '</div>';
    }

    // 松瓷机电AI助手状态
    html += '<div class="col-md-6 mb-3">';
    html += '<h6><i class="fas fa-robot me-2"></i>松瓷机电AI助手状态</h6>';
    html += '<ul class="list-unstyled">';
    html += '<li><strong>状态:</strong> ' + (status.ai_assistant.initialized ?
        '<span class="text-success">已初始化</span>' : '<span class="text-danger">未初始化</span>') + '</li>';

    if (status.ai_assistant.components) {
        Object.keys(status.ai_assistant.components).forEach(component => {
            const available = status.ai_assistant.components[component];
            html += '<li><strong>' + component + ':</strong> ' + (available ?
                '<span class="text-success">可用</span>' : '<span class="text-warning">不可用</span>') + '</li>';
        });
    }
    html += '</ul>';
    html += '</div>';

    html += '</div>';

    content.innerHTML = html;
}

function refreshSystemStatus() {
    document.getElementById('system-status-content').innerHTML =
        '<div class="text-center"><div class="spinner-border" role="status"></div><p class="mt-2">正在刷新...</p></div>';
    loadSystemStatus();
}
</script>
{% endblock %}
